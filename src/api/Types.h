#pragma once

#include <cstdint> // IWYU pragma: keep

using ushort = unsigned short;
using uint   = unsigned int;
using ulong  = unsigned long;
using llong  = long long;
using ullong = unsigned long long;

using uchar = unsigned char;
using schar = signed char;
using byte  = uchar;

using ldouble = long double;

using int64 = long long;
using int32 = int;
using int16 = short;
using int8  = char;

using uint64 = unsigned long long;
using uint32 = unsigned int;
using uint16 = unsigned short;
using uint8  = unsigned char;
