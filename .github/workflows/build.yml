name: Build

on:
  push:
  pull_request:

jobs:
  build-windows:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup xmake
        uses: xmake-io/github-action-setup-xmake@v1

      - name: Build for Windows (DLL)
        run: |
          xmake f -p windows -m release -v -y
          xmake

      - name: Upload DLL
        uses: actions/upload-artifact@v4
        with:
          name: windows
          path: build/windows/x64/release/*.dll

  build-android:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup xmake
        uses: xmake-io/github-action-setup-xmake@v1

      - name: Setup Android SDK
        uses: android-actions/setup-android@v3

      - name: Build for Android (SO)
        run: |
          xmake f -p android -a arm64-v8a -m release -v -y
          xmake

      - name: Upload SO
        uses: actions/upload-artifact@v4
        with:
          name: android
          path: build/android/arm64-v8a/release/*.so
