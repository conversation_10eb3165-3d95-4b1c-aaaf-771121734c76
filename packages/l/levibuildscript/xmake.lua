package("levibuildscript")
    add_urls("https://github.com/LiteLDev/LeviBuildScript.git")
    set_kind("binary")
    add_versions("0.1.0", "c114f86f40f7fc980deaff8c3e55f642278dd6f0")
    add_versions("0.2.0", "40dd5706fa1e0d7a6c8d17f6168e3148d8919c34")
    add_versions("0.3.0", "979786073b5a5f76ea3599c1f28e709f4924909c")
    add_versions("0.4.0", "f997d4e180be7e1b28f89f5f597f8873139113ba")
    add_versions("0.4.1", "9fb961b64f5e089ea65b5cd915dafb70d30320d1")
    on_install(function (package)
        os.cp("rules/*", package:installdir("rules"))
    end)
