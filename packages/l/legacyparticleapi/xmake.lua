package("legacyparticleapi")
    add_urls("https://github.com/LiteLDev/LegacyParticleAPI/releases/download/v$(version)/LegacyParticleAPI-windows-x64.zip")
    add_versions("0.3.0", "1ad52812d10e39d2606b1f6114989ad20c573e90c550725922b97445648929ff")
    add_versions("0.4.0", "88e22e46610776c9791fe83a976722b7629891776d2662535a13424c1a0bd3d8")
    add_versions("0.5.0", "8ea78e1923aa305359a607df7dc553f2bef998936f2bf9b8ee422658682768dd")
    add_versions("0.6.0", "5b792eb6e909781a87459d9165179bc78641464dc3b6b6fb6630741ddeeea386")
    add_versions("0.7.0", "f99339fcef7f99eb1a49cca4c6bdca7266340735d25f779a733b2bdebab4d023")
    add_versions("0.8.1", "65971dfecefb3ab6862a5ad485f34ad4618449ebae28aa5fb1a76ccc79ea18bd")
    add_versions("0.8.2", "410a21d7396746bd2bae3b3b4779b2c7f74a408c572507becd5d8e888e9a434d")
    add_versions("0.8.3", "5e373f1b28e1be9dce5ce11ad2dbb744b64efd66cfddd4ca3f458c563a4fe839")
    add_versions("0.9.0-rc.1", "6567f1849f4e5c5e8ba18805babf3b79f5afa10e63467efea54f8a55d44aa202")
    add_versions("0.10.0", "1d26e9a3dfc88fbf39a114b640c2b3eb6c32132e5b12ef9f39b924b492f4160c")

    on_install(function (package)
        os.cp("include", package:installdir())
        os.cp("lib/*.lib", package:installdir("lib"))
    end)
