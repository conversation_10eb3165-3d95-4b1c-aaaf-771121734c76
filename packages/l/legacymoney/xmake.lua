package("legacymoney")
    add_urls("https://github.com/LiteLDev/LegacyMoney/releases/download/v$(version)/LegacyMoney-windows-x64.zip")
    add_versions("0.3.0", "ce08d7524668a88a17645c87ced8ea15ca140b282e4c17db9035a3e875804240")
    add_versions("0.4.0", "f1a0fd84166a80c5082375203536a099da1e5204dfb3d20b1917c66f0c6bab58")
    add_versions("0.4.1", "6ffd7fcc9d93ee589a13d121208b9fc5047926e2268f59622798d27f4fef09e7")
    add_versions("0.5.0", "ee27b89450f30a2d4af09a2b53cc498a4d2e4399cc30038467a67cc6f1c06cdf")
    add_versions("0.6.0", "f985e973a7a8cdcb736d2ecd14dce8abbea76c1abe25c269661545ed52838400")
    add_versions("0.7.0", "6784da5c3ecf04e7a225c62283b6adf7a69566b01a1b7023d82913efb92203e3")
    add_versions("0.8.1", "f3d0713078335b8412c04e7287ae87f565492ea47b6db44131fe56ada6ece02f")
    add_versions("0.8.2", "2dd4e2ebdcb29f0e1b62d3ef94c1a589d72cd490827d8f585115ff82938043c0")
    add_versions("0.8.3", "a9aaf232a721194cebc32558aa6f421405388ff3d9d78ead20eb339e3a4f12c1")
    add_versions("0.9.0-rc.1", "3901c76a65f68e5d45fe6e3c09c4f6c45e1f766b284eb9a84064b44ad8b6e896")
    add_versions("0.10.0", "ecd0563833ca6afaece1be17e496eacda96ded42e1c40cd339039a139dd6407c")

    on_install(function (package)
        os.cp("include", package:installdir())
        os.cp("lib/*.lib", package:installdir("lib"))
    end)
