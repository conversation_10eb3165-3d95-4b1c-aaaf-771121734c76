package("node")
    add_urls("https://github.com/LiteLDev/node/releases/download/$(version)/node-win-x86_64.zip")
    add_versions("v16.16.0", "9b04418f18692c296afb3d4da8bfd0ce8794bfb371a7289c2cd00536cc516bee")
    add_versions("v22.12.0", "23aabeb38b45d5fb6bf702e770261086a8a5ed90c5848fb280a82436cb46aef2")

    add_includedirs("include/")
    add_includedirs("include/v8/")
    on_install(function (package)
        os.cp("*", package:installdir())
    end)
