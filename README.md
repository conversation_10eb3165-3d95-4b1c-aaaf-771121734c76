<p align="right">
  <b>Language: <a href="README.zh.md">简体中文</a></b>
</p>

# ForceCloseOreUI

A handy utility to force close the Minecraft OreUI.

---

## How to Use

### 🖥️ Windows

#### 1. Download the Latest DLL

- Go to the [Releases page](https://github.com/QYCottage/ForceCloseOreUI/releases) to download the latest `.dll` file.

#### 2. Installation

- **If you are using [QYCottage/mc-w10-version-launcher](https://github.com/QYCottage/mc-w10-version-launcher):**

  1. After downloading the desired Minecraft version via the launcher, open the game folder.
  2. Locate the `mods` folder and place the `.dll` file inside.
  3. Configuration file location:
     ```
     mods/ForceCloseOreUI/config.json
     ```

- **If you use an injector or another launcher:**
  1. Configuration file location:
     ```
     C:\Users\<USER>\AppData\Local\Packages\Microsoft.MinecraftUWP_8wekyb3d8bbwe\AC\mods\ForceCloseOreUI
     ```

---

### 📱 Android

#### 1. Download the Latest SO File

- Go to the [Releases page](https://github.com/QYCottage/ForceCloseOreUI/releases) to download the latest `.so` file.

#### 2. Installation

- **If you are using [LeviLauncher](https://github.com/LiteLDev/LeviLaunchroid):**
  1. Download the `.so` file.
  2. Tap the file and choose to open with LeviLauncher for import.
  3. Configuration file location:
     ```
     /storage/emulated/0/Android/data/[minecraft package name]/mods/ForceCloseOreUI
     ```
     > **Note:** Modified APK files are NOT provided.

---

## 📢 Discord Community

Join our Discord community for support and discussion!

[![Join Discord](https://img.shields.io/discord/8nGcV8QkKZ?logo=discord&style=for-the-badge&label=Discord)](https://discord.gg/8nGcV8QkKZ)

---

> Thank you for your support and for using our tool!
